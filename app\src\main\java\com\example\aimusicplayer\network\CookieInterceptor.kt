package com.example.aimusicplayer.network

import android.text.TextUtils
import android.util.Log
import com.example.aimusicplayer.MusicApplication
import okhttp3.HttpUrl
import okhttp3.Interceptor
import okhttp3.Request
import okhttp3.Response
import java.io.IOException
import java.net.URLEncoder

/**
 * Cookie拦截器
 * 为所有请求添加Cookie头，解决跨域请求问题
 * 根据api.txt文档说明：跨域请求需要带上withCredentials: true或手动传入cookie，否则可能会因为没带上cookie导致301错误
 */
class CookieInterceptor : Interceptor {
    companion object {
        private const val TAG = "CookieInterceptor"
    }

    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()

        // 获取应用中保存的Cookie
        var cookie: String? = null
        try {
            // 尝试从请求头中获取Cookie
            val headerCookie = originalRequest.header("Cookie")
            if (!TextUtils.isEmpty(headerCookie)) {
                cookie = headerCookie
                Log.d(TAG, "使用请求头中的Cookie: ${if (cookie!!.length > 20) "${cookie.substring(0, 10)}...${cookie.substring(cookie.length - 10)}" else cookie}")
            } else {
                // 尝试从全局应用实例获取Cookie
                if (originalRequest.tag() is MusicApplication) {
                    cookie = (originalRequest.tag() as MusicApplication).getCookie()
                } else {
                    // 如果请求没有标记应用实例，尝试从全局应用实例获取
                    try {
                        val app = MusicApplication.getInstance()
                        cookie = app.getCookie()
                        Log.d(TAG, "从应用实例获取Cookie: ${if (cookie.length > 20) "${cookie.substring(0, 10)}...${cookie.substring(cookie.length - 10)}" else cookie}")
                    } catch (e: Exception) {
                        Log.e(TAG, "获取应用实例失败", e)
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取Cookie失败", e)
        }

        // 如果Cookie为空，直接处理原始请求
        if (TextUtils.isEmpty(cookie)) {
            Log.w(TAG, "Cookie为空，使用原始请求: ${originalRequest.url}")
            return chain.proceed(originalRequest)
        }

        // 创建新的请求构建器
        val requestBuilder = originalRequest.newBuilder()

        // 添加Cookie头
        requestBuilder.header("Cookie", cookie!!)

        // 添加跨域请求相关头
        requestBuilder.header("Origin", "https://music.163.com")
        requestBuilder.header("Referer", "https://music.163.com/")

        // 对于GET请求，同时在URL中添加cookie参数（双重保险，解决某些跨域问题）
        if ("GET" == originalRequest.method) {
            try {
                // 获取原始URL
                val originalUrl = originalRequest.url

                // 检查URL是否已经包含cookie参数
                val existingCookie = originalUrl.queryParameter("cookie")
                if (!TextUtils.isEmpty(existingCookie)) {
                    Log.d(TAG, "URL已包含cookie参数: $existingCookie")
                } else {
                    // 提取MUSIC_U部分（如果有）
                    val musicUCookie = extractMusicUCookie(cookie)
                    if (!TextUtils.isEmpty(musicUCookie)) {
                        // 编码cookie值
                        val encodedCookie = URLEncoder.encode(musicUCookie, "UTF-8")

                        // 添加cookie参数到URL
                        val newUrl = originalUrl.newBuilder()
                            .addQueryParameter("cookie", encodedCookie)
                            .addQueryParameter("realIP", "**************") // 添加realIP参数解决可能的IP限制问题
                            .build()

                        // 更新请求URL
                        requestBuilder.url(newUrl)

                        Log.d(TAG, "添加cookie参数到URL: $encodedCookie")
                        Log.d(TAG, "原始URL: $originalUrl")
                        Log.d(TAG, "新URL: $newUrl")
                    } else {
                        Log.w(TAG, "未能提取MUSIC_U部分，使用完整cookie")
                        // 如果没有MUSIC_U部分，使用完整cookie
                        val encodedCookie = URLEncoder.encode(cookie, "UTF-8")
                        val newUrl = originalUrl.newBuilder()
                            .addQueryParameter("cookie", encodedCookie)
                            .addQueryParameter("realIP", "**************") // 添加realIP参数解决可能的IP限制问题
                            .build()
                        requestBuilder.url(newUrl)
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "添加cookie参数到URL失败", e)
            }
        }

        // 创建新的请求
        val newRequest = requestBuilder.build()

        Log.d(TAG, "最终请求URL: ${newRequest.url}")
        Log.d(TAG, "添加Cookie头: ${if (cookie.length > 20) "${cookie.substring(0, 10)}...${cookie.substring(cookie.length - 10)}" else cookie}")

        // 处理新的请求
        return chain.proceed(newRequest)
    }

    /**
     * 从完整cookie字符串中提取MUSIC_U部分
     * @param cookie 完整cookie字符串
     * @return MUSIC_U部分的值，如果没有则返回原始cookie
     */
    private fun extractMusicUCookie(cookie: String?): String? {
        if (TextUtils.isEmpty(cookie)) {
            return cookie
        }

        // 尝试提取MUSIC_U部分
        try {
            val cookieParts = cookie!!.split(";")
            for (part in cookieParts) {
                val trimmedPart = part.trim()
                if (trimmedPart.startsWith("MUSIC_U=")) {
                    Log.d(TAG, "成功提取MUSIC_U部分: $trimmedPart")
                    return trimmedPart
                }
            }
            Log.w(TAG, "未找到MUSIC_U部分")
        } catch (e: Exception) {
            Log.e(TAG, "提取MUSIC_U cookie失败", e)
        }

        // 如果没有找到MUSIC_U，返回原始cookie
        return cookie
    }
}
