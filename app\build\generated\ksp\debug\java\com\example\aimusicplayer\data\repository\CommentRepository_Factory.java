package com.example.aimusicplayer.data.repository;

import com.example.aimusicplayer.data.cache.ApiCacheManager;
import com.example.aimusicplayer.data.source.ApiService;
import com.example.aimusicplayer.network.ApiCallStrategy;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CommentRepository_Factory implements Factory<CommentRepository> {
  private final Provider<ApiService> apiServiceProvider;

  private final Provider<ApiCacheManager> apiCacheManagerProvider;

  private final Provider<ApiCallStrategy> apiCallStrategyProvider;

  public CommentRepository_Factory(Provider<ApiService> apiServiceProvider,
      Provider<ApiCacheManager> apiCacheManagerProvider,
      Provider<ApiCallStrategy> apiCallStrategyProvider) {
    this.apiServiceProvider = apiServiceProvider;
    this.apiCacheManagerProvider = apiCacheManagerProvider;
    this.apiCallStrategyProvider = apiCallStrategyProvider;
  }

  @Override
  public CommentRepository get() {
    CommentRepository instance = newInstance(apiServiceProvider.get());
    BaseRepository_MembersInjector.injectApiCacheManager(instance, apiCacheManagerProvider.get());
    BaseRepository_MembersInjector.injectApiCallStrategy(instance, apiCallStrategyProvider.get());
    return instance;
  }

  public static CommentRepository_Factory create(Provider<ApiService> apiServiceProvider,
      Provider<ApiCacheManager> apiCacheManagerProvider,
      Provider<ApiCallStrategy> apiCallStrategyProvider) {
    return new CommentRepository_Factory(apiServiceProvider, apiCacheManagerProvider, apiCallStrategyProvider);
  }

  public static CommentRepository newInstance(ApiService apiService) {
    return new CommentRepository(apiService);
  }
}
