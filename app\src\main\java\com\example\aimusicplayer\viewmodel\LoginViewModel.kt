package com.example.aimusicplayer.viewmodel

import android.app.Application
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import com.example.aimusicplayer.MusicApplication
import com.example.aimusicplayer.data.repository.UserRepository
import com.example.aimusicplayer.error.GlobalErrorHandler
import com.example.aimusicplayer.ui.login.QrCodeProcessor
import com.example.aimusicplayer.utils.NetworkResult
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import javax.inject.Inject

/**
 * 登录页的ViewModel
 * 负责处理登录页的业务逻辑
 * 使用Hilt依赖注入
 * 使用Flow管理UI状态
 */
@HiltViewModel
class LoginViewModel @Inject constructor(
    application: Application,
    private val userRepository: UserRepository,
    errorHandler: GlobalErrorHandler
) : FlowViewModel(application) {

    init {
        // 设置错误处理器
        this.errorHandler = errorHandler
    }

    companion object {
        private const val TAG = "LoginViewModel"
    }

    // 登录状态
    enum class LoginState {
        IDLE,       // 初始状态
        SUCCESS,    // 登录成功
        FAILED      // 登录失败
    }

    // 验证码状态
    enum class CaptchaState {
        IDLE,       // 初始状态
        SENT,       // 已发送
        VERIFIED,   // 已验证
        INVALID,    // 无效
        ERROR       // 错误
    }

    // 登录状态的StateFlow
    private val _loginStateFlow = MutableStateFlow(LoginState.IDLE)
    val loginStateFlow: StateFlow<LoginState> = _loginStateFlow.asStateFlow()
    val loginState: LiveData<LoginState> = loginStateFlow.asLiveData() // 兼容LiveData

    // 登录进度已经在FlowViewModel中定义，这里直接使用
    override val loading: LiveData<Boolean> = loadingFlow.asLiveData() // 兼容LiveData

    // 错误信息已经在FlowViewModel中定义，这里直接使用
    override val errorMessage: LiveData<String> = errorMessageFlow.asLiveData() // 兼容LiveData

    // 二维码处理器
    val qrCodeProcessor: QrCodeProcessor

    // 应用实例
    private val musicApplication: MusicApplication = getApplication() as MusicApplication

    init {
        // 初始化二维码处理器
        qrCodeProcessor = QrCodeProcessor(getApplication(), userRepository)

        // 观察二维码处理器的状态
        qrCodeProcessor.qrStatus.observeForever { status ->
            if (status == QrCodeProcessor.QrStatus.CONFIRMED) {
                // 二维码已确认，获取用户信息
                launchSafely {
                    // 获取用户信息
                    getUserInfo()
                }
            }
        }
    }

    /**
     * 获取二维码登录 - 参考ponymusic实现
     */
    fun getQrKey() {
        Log.d(TAG, "开始获取二维码登录")
        qrCodeProcessor.getLoginQrCode()
    }

    /**
     * 执行游客登录
     */
    fun loginAsGuest() {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "游客登录失败", e)
                handleError(e, "登录失败: ${e.message}")
                _loginStateFlow.value = LoginState.FAILED
            }
        ) {
            setLoading(true)
            try {
                // 获取当前时间戳，防止缓存
                val timestamp = System.currentTimeMillis()

                // 调用游客登录API
                val response = withContext(Dispatchers.IO) {
                    userRepository.guestLogin(timestamp)
                }

                // 解析响应
                val jsonObject = JSONObject(response)
                val code = jsonObject.optInt("code")

                if (code == 200) {
                    // 游客登录成功
                    val cookie = jsonObject.optString("cookie", "")

                    if (cookie.isNotEmpty()) {
                        // 保存Cookie
                        userRepository.saveCookie(cookie)

                        // 获取用户信息
                        getUserInfo()
                    } else {
                        // 尝试直接从响应中获取用户信息
                        if (jsonObject.has("account") && !jsonObject.isNull("account")) {
                            val account = jsonObject.getJSONObject("account")
                            val userId = account.optString("id", "")

                            if (userId.isNotEmpty()) {
                                // 生成游客名称
                                val guestName = "游客${userId.substring(Math.max(0, userId.length - 6))}"

                                // 保存用户信息
                                saveUserProfile(guestName, userId, "", true)

                                // 设置登录成功状态
                                _loginStateFlow.value = LoginState.SUCCESS
                            } else {
                                // 未获取到有效的用户ID
                                handleError(Exception("未能获取有效的用户ID"), "登录失败: 未能获取有效的用户ID")
                                _loginStateFlow.value = LoginState.FAILED
                            }
                        } else {
                            // 尝试调用登录状态API
                            checkLoginStatus()
                        }
                    }
                } else {
                    // 游客登录失败
                    val msg = jsonObject.optString("msg", "游客登录失败")
                    handleError(Exception(msg), msg)
                    _loginStateFlow.value = LoginState.FAILED
                }
            } finally {
                setLoading(false)
            }
        }
    }

    /**
     * 检查登录状态
     */
    private suspend fun checkLoginStatus() {
        try {
            // 调用登录状态API
            val response = withContext(Dispatchers.IO) {
                userRepository.checkLoginStatus()
            }

            // 解析响应
            val jsonObject = JSONObject(response)
            val code = jsonObject.optInt("code")

            if (code == 200) {
                // 登录状态检查成功
                val data = jsonObject.optJSONObject("data")

                if (data != null) {
                    val account = data.optJSONObject("account")
                    val profile = data.optJSONObject("profile")

                    if (account != null) {
                        val userId = account.optString("id", "")

                        if (userId.isNotEmpty()) {
                            // 获取用户名和头像
                            val (username, avatarUrl) = if (profile != null) {
                                Pair(
                                    profile.optString("nickname", ""),
                                    profile.optString("avatarUrl", "")
                                )
                            } else {
                                // 如果没有profile，使用account中的信息
                                val isAnonymousUser = account.optBoolean("anonimousUser", false)
                                val name = if (isAnonymousUser) {
                                    // 游客账号
                                    "游客${userId.substring(Math.max(0, userId.length - 6))}"
                                } else {
                                    // 其他情况，尝试从account获取userName
                                    account.optString("userName", "用户$userId")
                                }
                                Pair(name, "")
                            }

                            // 保存用户信息
                            saveUserProfile(username, userId, avatarUrl, true)

                            // 设置登录成功状态
                            _loginStateFlow.value = LoginState.SUCCESS
                        } else {
                            // 未获取到有效的用户ID
                            handleError(Exception("未能获取有效的用户ID"), "登录失败: 未能获取有效的用户ID")
                            _loginStateFlow.value = LoginState.FAILED
                        }
                    } else {
                        // 没有account字段
                        handleError(Exception("未能获取账号信息"), "登录失败: 未能获取账号信息")
                        _loginStateFlow.value = LoginState.FAILED
                    }
                } else {
                    // 没有data字段
                    handleError(Exception("响应中没有data字段"), "登录失败: 响应中没有data字段")
                    _loginStateFlow.value = LoginState.FAILED
                }
            } else {
                // API返回错误码
                val msg = jsonObject.optString("msg", "登录失败: 状态码 $code")
                handleError(Exception(msg), msg)
                _loginStateFlow.value = LoginState.FAILED
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查登录状态失败", e)
            handleError(e, "登录失败: ${e.message}")
            _loginStateFlow.value = LoginState.FAILED
        }
    }

    /**
     * 获取用户信息 - 基于API源码分析的正确实现
     */
    private suspend fun getUserInfo() {
        try {
            Log.d(TAG, "开始获取用户信息")

            // 方法1: 尝试登录状态检查API - 根据API源码，这个返回包装在data中的结构
            Log.d(TAG, "尝试登录状态检查API")
            val loginStatusResponse = withContext(Dispatchers.IO) {
                userRepository.checkLoginStatus()
            }

            Log.d(TAG, "登录状态检查响应: $loginStatusResponse")
            val loginStatusJson = JSONObject(loginStatusResponse)

            // 检查是否是包装格式 { status: 200, body: { data: {...} } }
            if (loginStatusJson.has("status") && loginStatusJson.optInt("status") == 200) {
                val body = loginStatusJson.optJSONObject("body")
                if (body != null) {
                    val data = body.optJSONObject("data")
                    if (data != null && data.has("account")) {
                        val account = data.getJSONObject("account")
                        val profile = data.optJSONObject("profile")

                        val userId = account.optString("id", "")
                        if (userId.isNotEmpty()) {
                            val username = profile?.optString("nickname", "") ?: "用户$userId"
                            val avatarUrl = profile?.optString("avatarUrl", "") ?: ""

                            saveUserProfile(username, userId, avatarUrl, true)
                            _loginStateFlow.value = LoginState.SUCCESS
                            Log.d(TAG, "通过登录状态检查获取用户信息成功")
                            return
                        }
                    }
                }
            }

            // 检查是否是直接格式 { code: 200, data: {...} }
            val loginStatusCode = loginStatusJson.optInt("code")
            if (loginStatusCode == 200) {
                val data = loginStatusJson.optJSONObject("data")
                if (data != null && data.has("account")) {
                    val account = data.getJSONObject("account")
                    val profile = data.optJSONObject("profile")

                    val userId = account.optString("id", "")
                    if (userId.isNotEmpty()) {
                        val username = profile?.optString("nickname", "") ?: "用户$userId"
                        val avatarUrl = profile?.optString("avatarUrl", "") ?: ""

                        saveUserProfile(username, userId, avatarUrl, true)
                        _loginStateFlow.value = LoginState.SUCCESS
                        Log.d(TAG, "通过登录状态检查获取用户信息成功")
                        return
                    }
                }

                // 检查是否直接在根级别有account字段
                if (loginStatusJson.has("account")) {
                    val account = loginStatusJson.getJSONObject("account")
                    val profile = loginStatusJson.optJSONObject("profile")

                    val userId = account.optString("id", "")
                    if (userId.isNotEmpty()) {
                        val username = profile?.optString("nickname", "") ?: "用户$userId"
                        val avatarUrl = profile?.optString("avatarUrl", "") ?: ""

                        saveUserProfile(username, userId, avatarUrl, true)
                        _loginStateFlow.value = LoginState.SUCCESS
                        Log.d(TAG, "通过登录状态检查获取用户信息成功")
                        return
                    }
                }
            }

            // 方法2: 尝试用户账号API - 根据API源码，这个直接返回account和profile
            Log.d(TAG, "尝试用户账号API")
            val userAccountResponse = withContext(Dispatchers.IO) {
                userRepository.getUserAccount()
            }

            Log.d(TAG, "用户账号信息响应: $userAccountResponse")
            val userAccountJson = JSONObject(userAccountResponse)
            val userAccountCode = userAccountJson.optInt("code")

            Log.d(TAG, "用户账号信息 - code: $userAccountCode")

            if (userAccountCode == 200) {
                if (userAccountJson.has("account")) {
                    val account = userAccountJson.getJSONObject("account")
                    val profile = userAccountJson.optJSONObject("profile")

                    val userId = account.optString("id", "")
                    if (userId.isNotEmpty()) {
                        val username = profile?.optString("nickname", "") ?: "用户$userId"
                        val avatarUrl = profile?.optString("avatarUrl", "") ?: ""

                        saveUserProfile(username, userId, avatarUrl, true)
                        _loginStateFlow.value = LoginState.SUCCESS
                        Log.d(TAG, "通过用户账号API获取用户信息成功")
                        return
                    }
                } else {
                    Log.e(TAG, "用户账号API响应中没有找到有效的account字段")
                    Log.d(TAG, "完整响应结构: ${userAccountJson.toString(2)}")
                }
            } else {
                Log.e(TAG, "用户账号API返回错误码: $userAccountCode")
                val msg = userAccountJson.optString("msg", "")
                Log.e(TAG, "错误信息: $msg")
            }

            // 所有方法都失败
            Log.e(TAG, "所有获取用户信息的方法都失败")
            handleError(Exception("无法获取用户信息"), "登录失败: 无法获取用户信息")
            _loginStateFlow.value = LoginState.FAILED

        } catch (e: Exception) {
            Log.e(TAG, "获取用户信息异常", e)
            handleError(e, "登录失败: ${e.message}")
            _loginStateFlow.value = LoginState.FAILED
        }
    }

    /**
     * 保存用户资料
     */
    fun saveUserProfile(username: String, userId: String, @Suppress("UNUSED_PARAMETER") avatarUrl: String, isLoggedIn: Boolean) {
        try {
            userRepository.saveLoginStatus(isLoggedIn, "user_token", userId, username)
            Log.d(TAG, "保存用户资料成功: $username")
        } catch (e: Exception) {
            Log.e(TAG, "保存用户资料失败", e)
            handleError(e, "保存用户资料失败: ${e.message}")
        }
    }

    /**
     * 发送验证码
     */
    fun sendCaptcha(phone: String, captchaState: MutableLiveData<CaptchaState>) {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "发送验证码失败", e)
                handleError(e, "发送验证码失败: ${e.message}")
                captchaState.value = CaptchaState.ERROR
            }
        ) {
            setLoading(true)
            try {
                // 调用API发送验证码
                val response = withContext(Dispatchers.IO) {
                    userRepository.sendCaptcha(phone)
                }

                // 解析响应
                val jsonObject = JSONObject(response)
                val code = jsonObject.optInt("code")

                if (code == 200) {
                    // 发送验证码成功
                    captchaState.value = CaptchaState.SENT
                } else {
                    // 发送验证码失败
                    val msg = jsonObject.optString("msg", "发送验证码失败: 状态码 $code")
                    handleError(Exception(msg), msg)
                    captchaState.value = CaptchaState.ERROR
                }
            } finally {
                setLoading(false)
            }
        }
    }

    /**
     * 验证验证码
     */
    fun verifyCaptcha(phone: String, captcha: String, captchaState: MutableLiveData<CaptchaState>) {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "验证验证码失败", e)
                handleError(e, "验证验证码失败: ${e.message}")
                captchaState.value = CaptchaState.ERROR
            }
        ) {
            setLoading(true)
            try {
                // 调用API验证验证码
                val response = withContext(Dispatchers.IO) {
                    userRepository.verifyCaptcha(phone, captcha)
                }

                // 解析响应
                val jsonObject = JSONObject(response)
                val code = jsonObject.optInt("code")

                if (code == 200) {
                    // 验证码验证成功
                    captchaState.value = CaptchaState.VERIFIED
                } else {
                    // 验证码验证失败
                    val msg = jsonObject.optString("msg", "验证码验证失败: 状态码 $code")
                    handleError(Exception(msg), msg)
                    captchaState.value = CaptchaState.INVALID
                }
            } finally {
                setLoading(false)
            }
        }
    }

    /**
     * 使用验证码登录
     */
    fun loginWithCaptcha(phone: String, captcha: String) {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "验证码登录失败", e)
                handleError(e, "登录失败: ${e.message}")
                _loginStateFlow.value = LoginState.FAILED
            }
        ) {
            setLoading(true)
            try {
                // 调用API使用验证码登录
                val response = withContext(Dispatchers.IO) {
                    userRepository.loginWithCaptcha(phone, captcha)
                }

                // 解析响应
                val jsonObject = JSONObject(response)
                val code = jsonObject.optInt("code")

                if (code == 200) {
                    // 登录成功
                    val cookie = jsonObject.optString("cookie", "")

                    if (cookie.isNotEmpty()) {
                        // 保存Cookie
                        userRepository.saveCookie(cookie)

                        // 获取用户信息
                        getUserInfo()
                    } else {
                        // 没有cookie
                        handleError(Exception("未能获取登录凭证"), "登录失败: 未能获取登录凭证")
                        _loginStateFlow.value = LoginState.FAILED
                    }
                } else {
                    // 登录失败
                    val msg = jsonObject.optString("msg", "登录失败: 状态码 $code")
                    handleError(Exception(msg), msg)
                    _loginStateFlow.value = LoginState.FAILED
                }
            } finally {
                setLoading(false)
            }
        }
    }

    /**
     * 使用手机号密码登录
     */
    fun loginWithPhone(phone: String, password: String) {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "手机号密码登录失败", e)
                handleError(e, "登录失败: ${e.message}")
                _loginStateFlow.value = LoginState.FAILED
            }
        ) {
            setLoading(true)
            try {
                // 调用API使用手机号密码登录
                val response = withContext(Dispatchers.IO) {
                    userRepository.loginWithPhone(phone, password)
                }

                // 解析响应
                val jsonObject = JSONObject(response)
                val code = jsonObject.optInt("code")

                if (code == 200) {
                    // 登录成功
                    val cookie = jsonObject.optString("cookie", "")

                    if (cookie.isNotEmpty()) {
                        // 保存Cookie
                        userRepository.saveCookie(cookie)

                        // 获取用户信息
                        getUserInfo()
                    } else {
                        // 没有cookie
                        handleError(Exception("未能获取登录凭证"), "登录失败: 未能获取登录凭证")
                        _loginStateFlow.value = LoginState.FAILED
                    }
                } else {
                    // 登录失败
                    val msg = jsonObject.optString("msg", "登录失败: 状态码 $code")
                    handleError(Exception(msg), msg)
                    _loginStateFlow.value = LoginState.FAILED
                }
            } finally {
                setLoading(false)
            }
        }
    }
}
