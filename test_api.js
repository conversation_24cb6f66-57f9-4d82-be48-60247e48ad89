// 完整的API测试和数据结构验证脚本
const https = require('https');
const fs = require('fs');
const path = require('path');

// API服务器配置
const API_SERVERS = {
    primary: '1355831898-4499wupl9z.ap-guangzhou.tencentscf.com',
    backup: 'zm.armoe.cn'
};

// 项目中使用的API接口配置
const API_ENDPOINTS = {
    // 搜索相关
    cloudsearch: {
        path: '/cloudsearch?keywords=%E5%91%A8%E6%9D%B0%E4%BC%A6&type=1&limit=5',
        expectedFields: ['code', 'result', 'result.songs', 'result.songCount'],
        description: '云搜索API'
    },
    searchSuggest: {
        path: '/search/suggest?keywords=%E5%91%A8%E6%9D%B0%E4%BC%A6&type=mobile',
        expectedFields: ['code', 'result'],
        description: '搜索建议API'
    },

    // 音乐相关
    songDetail: {
        path: '/song/detail?ids=347230',
        expectedFields: ['code', 'songs', 'songs[0].id', 'songs[0].name', 'songs[0].al'],
        description: '歌曲详情API'
    },
    lyric: {
        path: '/lyric?id=347230',
        expectedFields: ['code', 'lrc', 'lrc.lyric', 'tlyric'],
        description: '歌词API'
    },
    topSong: {
        path: '/top/song?type=0',
        expectedFields: ['code', 'data'],
        description: '新歌速递API'
    },
    songUrl: {
        path: '/song/url?id=347230',
        expectedFields: ['code', 'data'],
        description: '歌曲播放URL API'
    },

    // 用户相关
    loginStatus: {
        path: '/login/status',
        expectedFields: ['status'],
        description: '登录状态检查API'
    },
    userAccount: {
        path: '/user/account',
        expectedFields: ['code'],
        description: '用户账号API'
    },

    // 收藏和评论
    commentMusic: {
        path: '/comment/music?id=347230&limit=5',
        expectedFields: ['code', 'comments', 'hotComments'],
        description: '歌曲评论API'
    }
};

// 测试结果存储
let testResults = {
    primary: {},
    backup: {},
    summary: {
        total: 0,
        passed: 0,
        failed: 0,
        primaryAvailable: 0,
        backupAvailable: 0
    }
};

// 颜色输出函数
const colors = {
    green: (text) => `\x1b[32m${text}\x1b[0m`,
    red: (text) => `\x1b[31m${text}\x1b[0m`,
    yellow: (text) => `\x1b[33m${text}\x1b[0m`,
    blue: (text) => `\x1b[34m${text}\x1b[0m`,
    cyan: (text) => `\x1b[36m${text}\x1b[0m`,
    bold: (text) => `\x1b[1m${text}\x1b[0m`
};

// 检查字段是否存在
function checkFields(obj, fields) {
    const results = [];
    for (const field of fields) {
        const exists = hasNestedProperty(obj, field);
        results.push({
            field,
            exists,
            value: exists ? getNestedProperty(obj, field) : undefined
        });
    }
    return results;
}

// 检查嵌套属性是否存在
function hasNestedProperty(obj, path) {
    try {
        const keys = path.split(/[\.\[\]]+/).filter(key => key !== '');
        let current = obj;

        for (const key of keys) {
            if (key === '0' && Array.isArray(current) && current.length > 0) {
                current = current[0];
            } else if (current && typeof current === 'object' && key in current) {
                current = current[key];
            } else {
                return false;
            }
        }
        return true;
    } catch (e) {
        return false;
    }
}

// 获取嵌套属性值
function getNestedProperty(obj, path) {
    try {
        const keys = path.split(/[\.\[\]]+/).filter(key => key !== '');
        let current = obj;

        for (const key of keys) {
            if (key === '0' && Array.isArray(current) && current.length > 0) {
                current = current[0];
            } else if (current && typeof current === 'object' && key in current) {
                current = current[key];
            } else {
                return undefined;
            }
        }
        return current;
    } catch (e) {
        return undefined;
    }
}

// 测试单个API接口
function testApiEndpoint(serverKey, hostname, endpoint, config) {
    return new Promise((resolve) => {
        const startTime = Date.now();

        const options = {
            hostname: hostname,
            path: config.path,
            method: 'GET',
            timeout: 10000,
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
            }
        };

        const req = https.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                const responseTime = Date.now() - startTime;
                let result = {
                    endpoint,
                    server: serverKey,
                    hostname,
                    success: false,
                    responseTime,
                    statusCode: res.statusCode,
                    error: null,
                    dataStructure: null,
                    fieldCheck: null
                };

                try {
                    const json = JSON.parse(data);
                    result.success = true;
                    result.dataStructure = json;
                    result.fieldCheck = checkFields(json, config.expectedFields);

                    // 检查是否所有必需字段都存在
                    const allFieldsExist = result.fieldCheck.every(field => field.exists);
                    result.structureValid = allFieldsExist;

                } catch (e) {
                    result.error = `JSON解析失败: ${e.message}`;
                    result.rawData = data.substring(0, 500); // 只保留前500字符
                }

                resolve(result);
            });
        });

        req.on('error', (e) => {
            const responseTime = Date.now() - startTime;
            resolve({
                endpoint,
                server: serverKey,
                hostname,
                success: false,
                responseTime,
                error: `请求失败: ${e.message}`,
                dataStructure: null,
                fieldCheck: null,
                structureValid: false
            });
        });

        req.on('timeout', () => {
            req.destroy();
            const responseTime = Date.now() - startTime;
            resolve({
                endpoint,
                server: serverKey,
                hostname,
                success: false,
                responseTime,
                error: '请求超时',
                dataStructure: null,
                fieldCheck: null,
                structureValid: false
            });
        });

        req.end();
    });
}

// 执行所有API测试
async function runAllTests() {
    console.log(colors.bold('\n🚀 开始API接口测试和数据结构验证\n'));
    console.log(colors.cyan('测试服务器:'));
    console.log(`  主服务器: ${API_SERVERS.primary}`);
    console.log(`  备用服务器: ${API_SERVERS.backup}\n`);

    const endpoints = Object.keys(API_ENDPOINTS);
    testResults.summary.total = endpoints.length * 2; // 两个服务器

    for (const endpoint of endpoints) {
        const config = API_ENDPOINTS[endpoint];
        console.log(colors.blue(`\n📡 测试接口: ${config.description} (${endpoint})`));

        // 测试主服务器
        console.log(colors.yellow(`  ├─ 主服务器测试中...`));
        const primaryResult = await testApiEndpoint('primary', API_SERVERS.primary, endpoint, config);
        testResults.primary[endpoint] = primaryResult;

        if (primaryResult.success && primaryResult.structureValid) {
            console.log(colors.green(`  ├─ ✅ 主服务器: 成功 (${primaryResult.responseTime}ms)`));
            testResults.summary.primaryAvailable++;
        } else {
            console.log(colors.red(`  ├─ ❌ 主服务器: ${primaryResult.error || '数据结构不匹配'}`));
        }

        // 测试备用服务器
        console.log(colors.yellow(`  └─ 备用服务器测试中...`));
        const backupResult = await testApiEndpoint('backup', API_SERVERS.backup, endpoint, config);
        testResults.backup[endpoint] = backupResult;

        if (backupResult.success && backupResult.structureValid) {
            console.log(colors.green(`     ✅ 备用服务器: 成功 (${backupResult.responseTime}ms)`));
            testResults.summary.backupAvailable++;
        } else {
            console.log(colors.red(`     ❌ 备用服务器: ${backupResult.error || '数据结构不匹配'}`));
        }

        // 更新统计
        if ((primaryResult.success && primaryResult.structureValid) ||
            (backupResult.success && backupResult.structureValid)) {
            testResults.summary.passed++;
        } else {
            testResults.summary.failed++;
        }
    }
}

// 显示详细测试结果
function displayDetailedResults() {
    console.log(colors.bold('\n📊 详细测试结果分析\n'));

    const endpoints = Object.keys(API_ENDPOINTS);

    for (const endpoint of endpoints) {
        const config = API_ENDPOINTS[endpoint];
        const primaryResult = testResults.primary[endpoint];
        const backupResult = testResults.backup[endpoint];

        console.log(colors.cyan(`\n🔍 ${config.description} (${endpoint})`));
        console.log(`   接口路径: ${config.path}`);

        // 主服务器结果
        console.log(colors.blue('\n   主服务器结果:'));
        if (primaryResult.success) {
            console.log(colors.green(`   ✅ 请求成功 (${primaryResult.responseTime}ms)`));
            console.log(`   📄 状态码: ${primaryResult.statusCode}`);

            if (primaryResult.structureValid) {
                console.log(colors.green('   ✅ 数据结构验证通过'));
            } else {
                console.log(colors.red('   ❌ 数据结构验证失败'));
                console.log('   🔍 字段检查结果:');
                primaryResult.fieldCheck.forEach(field => {
                    const status = field.exists ? colors.green('✅') : colors.red('❌');
                    console.log(`      ${status} ${field.field}: ${field.exists ? '存在' : '缺失'}`);
                });
            }
        } else {
            console.log(colors.red(`   ❌ 请求失败: ${primaryResult.error}`));
        }

        // 备用服务器结果
        console.log(colors.blue('\n   备用服务器结果:'));
        if (backupResult.success) {
            console.log(colors.green(`   ✅ 请求成功 (${backupResult.responseTime}ms)`));
            console.log(`   📄 状态码: ${backupResult.statusCode}`);

            if (backupResult.structureValid) {
                console.log(colors.green('   ✅ 数据结构验证通过'));
            } else {
                console.log(colors.red('   ❌ 数据结构验证失败'));
                console.log('   🔍 字段检查结果:');
                backupResult.fieldCheck.forEach(field => {
                    const status = field.exists ? colors.green('✅') : colors.red('❌');
                    console.log(`      ${status} ${field.field}: ${field.exists ? '存在' : '缺失'}`);
                });
            }
        } else {
            console.log(colors.red(`   ❌ 请求失败: ${backupResult.error}`));
        }

        // 项目兼容性分析
        console.log(colors.yellow('\n   📋 项目兼容性分析:'));
        const primaryValid = primaryResult.success && primaryResult.structureValid;
        const backupValid = backupResult.success && backupResult.structureValid;

        if (primaryValid && backupValid) {
            console.log(colors.green('   ✅ 两个服务器都可用，项目可正常运行'));
        } else if (primaryValid || backupValid) {
            const workingServer = primaryValid ? '主服务器' : '备用服务器';
            console.log(colors.yellow(`   ⚠️  仅${workingServer}可用，建议检查另一服务器`));
        } else {
            console.log(colors.red('   ❌ 两个服务器都不可用，项目可能无法正常运行'));
        }
    }
}

// 显示测试总结
function displaySummary() {
    console.log(colors.bold('\n📈 测试总结报告\n'));

    const { total, passed, failed, primaryAvailable, backupAvailable } = testResults.summary;
    const successRate = ((passed / (total / 2)) * 100).toFixed(1);
    const primaryRate = ((primaryAvailable / (total / 2)) * 100).toFixed(1);
    const backupRate = ((backupAvailable / (total / 2)) * 100).toFixed(1);

    console.log(`📊 总体统计:`);
    console.log(`   测试接口总数: ${total / 2}`);
    console.log(`   成功接口数量: ${passed}`);
    console.log(`   失败接口数量: ${failed}`);
    console.log(`   总体成功率: ${colors.bold(successRate + '%')}\n`);

    console.log(`🖥️  服务器可用性:`);
    console.log(`   主服务器可用率: ${colors.bold(primaryRate + '%')} (${primaryAvailable}/${total / 2})`);
    console.log(`   备用服务器可用率: ${colors.bold(backupRate + '%')} (${backupAvailable}/${total / 2})\n`);

    // 建议
    console.log(`💡 建议:`);
    if (successRate >= 90) {
        console.log(colors.green('   ✅ API接口状态良好，项目可正常运行'));
    } else if (successRate >= 70) {
        console.log(colors.yellow('   ⚠️  部分API接口存在问题，建议检查失败的接口'));
    } else {
        console.log(colors.red('   ❌ 多个API接口存在问题，建议优先修复'));
    }

    if (primaryRate < 50 && backupRate >= 70) {
        console.log(colors.yellow('   💡 建议将备用服务器设为主服务器'));
    } else if (backupRate < 50 && primaryRate >= 70) {
        console.log(colors.yellow('   💡 主服务器状态良好，备用服务器需要检查'));
    }
}

// 生成测试报告文件
function generateReport() {
    const reportData = {
        timestamp: new Date().toISOString(),
        servers: API_SERVERS,
        results: testResults,
        summary: {
            ...testResults.summary,
            successRate: ((testResults.summary.passed / (testResults.summary.total / 2)) * 100).toFixed(1),
            primaryRate: ((testResults.summary.primaryAvailable / (testResults.summary.total / 2)) * 100).toFixed(1),
            backupRate: ((testResults.summary.backupAvailable / (testResults.summary.total / 2)) * 100).toFixed(1)
        }
    };

    const reportPath = path.join(__dirname, 'api_test_report.json');
    fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2), 'utf8');

    console.log(colors.cyan(`\n📄 详细测试报告已保存到: ${reportPath}`));
}

// 主执行函数
async function main() {
    try {
        await runAllTests();
        displayDetailedResults();
        displaySummary();
        generateReport();

        console.log(colors.bold('\n🎉 API测试完成！\n'));

        // 根据测试结果设置退出码
        const successRate = (testResults.summary.passed / (testResults.summary.total / 2)) * 100;
        process.exit(successRate >= 70 ? 0 : 1);

    } catch (error) {
        console.error(colors.red(`\n❌ 测试过程中发生错误: ${error.message}`));
        process.exit(1);
    }
}

// 启动测试
if (require.main === module) {
    main();
}
