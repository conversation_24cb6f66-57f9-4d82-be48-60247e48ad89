<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- 背景图层 -->
    <ImageView
        android:id="@+id/background_blur"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:contentDescription="背景"
        android:alpha="0.8" />

    <!-- 半透明遮罩层，增加对比度 -->
    <View
        android:id="@+id/background_overlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/color_black"
        android:alpha="0.6" />

    <!-- 主要内容区域 - 歌曲信息在左侧，歌词/评论/播放列表在右侧 -->
    <LinearLayout
        android:id="@+id/content_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/control_container"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- 左侧专辑和歌曲信息区域 -->
        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="2"
            android:gravity="center_vertical">

            <!-- 黑胶唱片和专辑封面 - 使用自定义AlbumCoverView，增大尺寸提升视觉效果 -->
            <com.example.aimusicplayer.ui.widget.AlbumCoverView
                android:id="@+id/album_cover_view"
                android:layout_width="480dp"
                android:layout_height="480dp"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="20dp" />

            <!-- 保留原有的ImageView作为备用 -->
            <ImageView
                android:id="@+id/album_art"
                android:layout_width="320dp"
                android:layout_height="320dp"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="100dp"
                android:scaleType="centerCrop"
                android:background="@drawable/album_art_border"
                android:contentDescription="专辑封面"
                android:elevation="2dp"
                android:visibility="gone"
                tools:src="@drawable/default_album_art" />

            <!-- 黑胶唱片底盘 - 保留用于旋转动画 -->
            <ImageView
                android:id="@+id/vinyl_background"
                android:layout_width="480dp"
                android:layout_height="480dp"
                android:layout_centerHorizontal="true"
                android:src="@drawable/vinyl_record"
                android:contentDescription="黑胶唱片"
                android:elevation="1dp"
                android:visibility="gone" />

            <!-- 歌曲和艺术家信息 - 紧贴唱片下方，尺寸适中 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/album_cover_view"
                android:layout_marginTop="16dp"
                android:layout_marginHorizontal="20dp"
                android:orientation="vertical"
                android:gravity="center">

                <TextView
                    android:id="@+id/song_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="marquee"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:singleLine="true"
                    android:focusable="true"
                    android:focusableInTouchMode="true"
                    android:scrollHorizontally="true"
                    android:textColor="@color/text_light"
                    android:textSize="22sp"
                    android:textStyle="bold"
                    android:gravity="center"
                    android:shadowColor="#80000000"
                    android:shadowDx="1"
                    android:shadowDy="1"
                    android:shadowRadius="3"
                    android:text="暂无"
                    tools:text="Love Me Not" />

                <TextView
                    android:id="@+id/song_artist"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:ellipsize="marquee"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:singleLine="true"
                    android:focusable="true"
                    android:focusableInTouchMode="true"
                    android:scrollHorizontally="true"
                    android:textColor="@color/color_gray_300"
                    android:textSize="16sp"
                    android:gravity="center"
                    android:shadowColor="#80000000"
                    android:shadowDx="1"
                    android:shadowDy="1"
                    android:shadowRadius="2"
                    android:text="暂无"
                    tools:text="Ravyn Lenae" />
            </LinearLayout>
        </RelativeLayout>

        <!-- 右侧内容区域 - 使用ViewPager2实现歌词、评论和播放列表的切换 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="3"
            android:layout_marginStart="16dp"
            android:orientation="vertical">

            <!-- 搜索功能区域 - 右上角短搜索框 -->
            <RelativeLayout
                android:id="@+id/search_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginEnd="20dp"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="8dp">

                <!-- 搜索框 - 初始状态较短，点击后延长一倍，与下方框架长度一致 -->
                <EditText
                    android:id="@+id/search_edit_text"
                    android:layout_width="140dp"
                    android:layout_height="44dp"
                    android:layout_toStartOf="@+id/search_button"
                    android:layout_marginEnd="8dp"
                    android:layout_alignParentEnd="true"
                    android:background="@drawable/sakura_search_background"
                    android:hint="搜索歌曲..."
                    android:textColorHint="#80FFB6C1"
                    android:textColor="@color/text_light"
                    android:textSize="14sp"
                    android:paddingHorizontal="12dp"
                    android:paddingVertical="8dp"
                    android:imeOptions="actionSearch"
                    android:inputType="text"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:visibility="visible" />

                <!-- 搜索按钮 - 右上角位置，樱花主题，增大尺寸 -->
                <ImageView
                    android:id="@+id/search_button"
                    android:layout_width="52dp"
                    android:layout_height="52dp"
                    android:layout_alignParentEnd="true"
                    android:background="@drawable/sakura_search_button_background"
                    android:src="@drawable/ic_search_white"
                    android:contentDescription="搜索"
                    android:padding="12dp"
                    android:clickable="true"
                    android:focusable="true" />
            </RelativeLayout>

            <!-- 搜索建议列表 - 樱花主题，宽度与展开后搜索框一致 -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/search_suggestions_recycler"
                android:layout_width="280dp"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_marginEnd="20dp"
                android:maxHeight="160dp"
                android:background="@drawable/sakura_search_suggestions_background"
                android:visibility="gone"
                android:elevation="4dp"
                android:nestedScrollingEnabled="false" />

            <!-- 搜索结果列表 - 樱花主题，宽度与展开后搜索框一致 -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/search_results_recycler"
                android:layout_width="280dp"
                android:layout_height="160dp"
                android:layout_gravity="end"
                android:layout_marginEnd="20dp"
                android:layout_marginTop="8dp"
                android:background="@drawable/sakura_search_results_background"
                android:visibility="gone"
                android:elevation="2dp" />

            <!-- 内容标签页 -->
            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tab_layout_player"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@android:color/transparent"
                app:tabIndicatorColor="?colorAccent"
                app:tabSelectedTextColor="?colorAccent"
                app:tabTextColor="@color/color_gray_500"
                app:tabMode="fixed"
                app:tabGravity="center"
                android:visibility="gone" />

            <!-- 内容区域 - 直接使用ViewPager2，移除CardView -->
            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/view_pager_player"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:layout_marginHorizontal="20dp"
                android:layout_marginVertical="10dp" />
        </LinearLayout>
    </LinearLayout>

    <!-- 加载动画 -->
    <com.example.aimusicplayer.ui.widget.LottieLoadingView
        android:id="@+id/loading_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:visibility="gone"
        android:elevation="10dp"
        app:lottieAnimationAsset="music_loading.json"
        app:loadingMessage="加载中..."
        app:autoPlay="true"
        app:loop="true" />

    <!-- 底部控制区域 - 极简透明设计，最小高度 -->
    <LinearLayout
        android:id="@+id/control_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:orientation="vertical"
        android:background="@color/color_gray_900"
        android:alpha="0.5"
        android:padding="8dp"
        android:layout_marginHorizontal="12dp"
        android:layout_marginBottom="8dp">

        <!-- 进度条和时间 - 最小间距 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="6dp"
            android:layout_marginHorizontal="8dp">

            <TextView
                android:id="@+id/textview_player_current_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_light"
                android:textSize="18sp"
                android:textStyle="bold"
                android:layout_marginEnd="10dp"
                android:text="00:00" />

            <SeekBar
                android:id="@+id/seekbar_player_progress"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:progressTint="@color/theme_accent"
                android:thumbTint="@color/theme_accent"
                android:minHeight="8dp"
                android:maxHeight="8dp"
                android:layout_gravity="center_vertical"
                android:thumbOffset="8dp" />

            <TextView
                android:id="@+id/textview_player_total_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_light"
                android:textSize="18sp"
                android:textStyle="bold"
                android:layout_marginStart="10dp"
                android:text="00:00" />
        </LinearLayout>

        <!-- 播放控制按钮 - 均匀分布，增大尺寸，删除波纹效果 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:layout_marginHorizontal="30dp">

            <!-- 歌曲列表按钮 -->
            <ImageView
                android:id="@+id/button_player_playlist"
                android:layout_width="0dp"
                android:layout_height="72dp"
                android:layout_weight="1"
                android:layout_marginHorizontal="4dp"
                android:padding="10dp"
                android:src="@drawable/ic_playlist"
                android:contentDescription="歌曲列表"
                android:clickable="true"
                android:focusable="true"
                android:background="@drawable/control_button_animated"
                android:elevation="2dp" />

            <!-- 播放模式按钮 -->
            <ImageView
                android:id="@+id/button_player_play_mode"
                android:layout_width="0dp"
                android:layout_height="72dp"
                android:layout_weight="1"
                android:layout_marginHorizontal="4dp"
                android:padding="10dp"
                android:src="@drawable/ic_repeat"
                android:contentDescription="播放模式"
                android:clickable="true"
                android:focusable="true"
                android:background="@drawable/control_button_animated"
                android:elevation="2dp" />

            <!-- 上一首按钮 -->
            <ImageView
                android:id="@+id/button_player_prev"
                android:layout_width="0dp"
                android:layout_height="72dp"
                android:layout_weight="1"
                android:layout_marginHorizontal="4dp"
                android:padding="10dp"
                android:src="@drawable/ic_previous"
                android:contentDescription="上一首"
                android:clickable="true"
                android:focusable="true"
                android:background="@drawable/control_button_animated"
                android:elevation="2dp" />

            <!-- 播放/暂停按钮 - 蓝色正圆形背景，增大尺寸，带点击动画 -->
            <ImageView
                android:id="@+id/button_player_play_pause"
                android:layout_width="88dp"
                android:layout_height="88dp"
                android:layout_marginHorizontal="8dp"
                android:padding="18dp"
                android:src="@drawable/ic_playing_play_pause_selector"
                android:contentDescription="播放/暂停"
                android:clickable="true"
                android:focusable="true"
                android:background="@drawable/play_button_click_animation"
                android:elevation="4dp"
                android:stateListAnimator="@null" />

            <!-- 下一首按钮 -->
            <ImageView
                android:id="@+id/button_player_next"
                android:layout_width="0dp"
                android:layout_height="72dp"
                android:layout_weight="1"
                android:layout_marginHorizontal="4dp"
                android:padding="10dp"
                android:src="@drawable/ic_next"
                android:contentDescription="下一首"
                android:clickable="true"
                android:focusable="true"
                android:background="@drawable/control_button_animated"
                android:elevation="2dp" />

            <!-- 评论按钮 -->
            <ImageView
                android:id="@+id/button_player_comment"
                android:layout_width="0dp"
                android:layout_height="72dp"
                android:layout_weight="1"
                android:layout_marginHorizontal="4dp"
                android:padding="10dp"
                android:src="@drawable/ic_comment"
                android:contentDescription="评论"
                android:clickable="true"
                android:focusable="true"
                android:background="@drawable/control_button_animated"
                android:elevation="2dp" />

            <!-- 收藏按钮 -->
            <ImageView
                android:id="@+id/button_player_collect"
                android:layout_width="0dp"
                android:layout_height="72dp"
                android:layout_weight="1"
                android:layout_marginHorizontal="4dp"
                android:padding="10dp"
                android:src="@drawable/ic_favorite_selector"
                android:contentDescription="收藏"
                android:clickable="true"
                android:focusable="true"
                android:background="@drawable/control_button_animated"
                android:elevation="2dp" />
        </LinearLayout>
    </LinearLayout>
</RelativeLayout>