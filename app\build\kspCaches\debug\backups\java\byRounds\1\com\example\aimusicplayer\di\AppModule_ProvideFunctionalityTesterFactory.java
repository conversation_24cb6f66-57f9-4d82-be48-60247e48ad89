package com.example.aimusicplayer.di;

import android.content.Context;
import com.example.aimusicplayer.data.repository.MusicRepository;
import com.example.aimusicplayer.data.repository.UserRepository;
import com.example.aimusicplayer.data.source.ApiService;
import com.example.aimusicplayer.utils.FunctionalityTester;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppModule_ProvideFunctionalityTesterFactory implements Factory<FunctionalityTester> {
  private final Provider<Context> contextProvider;

  private final Provider<ApiService> apiServiceProvider;

  private final Provider<MusicRepository> musicRepositoryProvider;

  private final Provider<UserRepository> userRepositoryProvider;

  public AppModule_ProvideFunctionalityTesterFactory(Provider<Context> contextProvider,
      Provider<ApiService> apiServiceProvider, Provider<MusicRepository> musicRepositoryProvider,
      Provider<UserRepository> userRepositoryProvider) {
    this.contextProvider = contextProvider;
    this.apiServiceProvider = apiServiceProvider;
    this.musicRepositoryProvider = musicRepositoryProvider;
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public FunctionalityTester get() {
    return provideFunctionalityTester(contextProvider.get(), apiServiceProvider.get(), musicRepositoryProvider.get(), userRepositoryProvider.get());
  }

  public static AppModule_ProvideFunctionalityTesterFactory create(
      Provider<Context> contextProvider, Provider<ApiService> apiServiceProvider,
      Provider<MusicRepository> musicRepositoryProvider,
      Provider<UserRepository> userRepositoryProvider) {
    return new AppModule_ProvideFunctionalityTesterFactory(contextProvider, apiServiceProvider, musicRepositoryProvider, userRepositoryProvider);
  }

  public static FunctionalityTester provideFunctionalityTester(Context context,
      ApiService apiService, MusicRepository musicRepository, UserRepository userRepository) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideFunctionalityTester(context, apiService, musicRepository, userRepository));
  }
}
