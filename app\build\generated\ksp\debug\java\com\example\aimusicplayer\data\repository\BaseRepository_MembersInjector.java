package com.example.aimusicplayer.data.repository;

import com.example.aimusicplayer.data.cache.ApiCacheManager;
import com.example.aimusicplayer.network.ApiCallStrategy;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class BaseRepository_MembersInjector implements MembersInjector<BaseRepository> {
  private final Provider<ApiCacheManager> apiCacheManagerProvider;

  private final Provider<ApiCallStrategy> apiCallStrategyProvider;

  public BaseRepository_MembersInjector(Provider<ApiCacheManager> apiCacheManagerProvider,
      Provider<ApiCallStrategy> apiCallStrategyProvider) {
    this.apiCacheManagerProvider = apiCacheManagerProvider;
    this.apiCallStrategyProvider = apiCallStrategyProvider;
  }

  public static MembersInjector<BaseRepository> create(
      Provider<ApiCacheManager> apiCacheManagerProvider,
      Provider<ApiCallStrategy> apiCallStrategyProvider) {
    return new BaseRepository_MembersInjector(apiCacheManagerProvider, apiCallStrategyProvider);
  }

  @Override
  public void injectMembers(BaseRepository instance) {
    injectApiCacheManager(instance, apiCacheManagerProvider.get());
    injectApiCallStrategy(instance, apiCallStrategyProvider.get());
  }

  @InjectedFieldSignature("com.example.aimusicplayer.data.repository.BaseRepository.apiCacheManager")
  public static void injectApiCacheManager(BaseRepository instance,
      ApiCacheManager apiCacheManager) {
    instance.apiCacheManager = apiCacheManager;
  }

  @InjectedFieldSignature("com.example.aimusicplayer.data.repository.BaseRepository.apiCallStrategy")
  public static void injectApiCallStrategy(BaseRepository instance,
      ApiCallStrategy apiCallStrategy) {
    instance.apiCallStrategy = apiCallStrategy;
  }
}
