package com.example.aimusicplayer.utils;

import android.content.Context;
import com.example.aimusicplayer.data.repository.MusicRepository;
import com.example.aimusicplayer.data.repository.UserRepository;
import com.example.aimusicplayer.data.source.ApiService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class FunctionalityTester_Factory implements Factory<FunctionalityTester> {
  private final Provider<Context> contextProvider;

  private final Provider<ApiService> apiServiceProvider;

  private final Provider<MusicRepository> musicRepositoryProvider;

  private final Provider<UserRepository> userRepositoryProvider;

  public FunctionalityTester_Factory(Provider<Context> contextProvider,
      Provider<ApiService> apiServiceProvider, Provider<MusicRepository> musicRepositoryProvider,
      Provider<UserRepository> userRepositoryProvider) {
    this.contextProvider = contextProvider;
    this.apiServiceProvider = apiServiceProvider;
    this.musicRepositoryProvider = musicRepositoryProvider;
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public FunctionalityTester get() {
    return newInstance(contextProvider.get(), apiServiceProvider.get(), musicRepositoryProvider.get(), userRepositoryProvider.get());
  }

  public static FunctionalityTester_Factory create(Provider<Context> contextProvider,
      Provider<ApiService> apiServiceProvider, Provider<MusicRepository> musicRepositoryProvider,
      Provider<UserRepository> userRepositoryProvider) {
    return new FunctionalityTester_Factory(contextProvider, apiServiceProvider, musicRepositoryProvider, userRepositoryProvider);
  }

  public static FunctionalityTester newInstance(Context context, ApiService apiService,
      MusicRepository musicRepository, UserRepository userRepository) {
    return new FunctionalityTester(context, apiService, musicRepository, userRepository);
  }
}
