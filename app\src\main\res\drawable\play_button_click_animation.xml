<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 按下状态 - 稍微缩小 -->
    <item android:state_pressed="true">
        <layer-list>
            <item>
                <shape android:shape="oval">
                    <gradient
                        android:startColor="#FF4A90E2"
                        android:centerColor="#FF3A80D8"
                        android:endColor="#FF2E7BD6"
                        android:type="radial"
                        android:gradientRadius="42dp" />
                    <stroke
                        android:width="3dp"
                        android:color="#FFFFFF" />
                    <size
                        android:width="84dp"
                        android:height="84dp" />
                </shape>
            </item>
        </layer-list>
    </item>

    <!-- 正常状态 -->
    <item>
        <shape android:shape="oval">
            <gradient
                android:startColor="#FF4A90E2"
                android:centerColor="#FF3A80D8"
                android:endColor="#FF2E7BD6"
                android:type="radial"
                android:gradientRadius="44dp" />
            <stroke
                android:width="3dp"
                android:color="#FFFFFF" />
            <size
                android:width="88dp"
                android:height="88dp" />
        </shape>
    </item>
</selector>
