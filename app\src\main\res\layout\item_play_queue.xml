<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?android:attr/selectableItemBackground"
    android:gravity="center_vertical"
    android:minHeight="@dimen/touch_target_size"
    android:orientation="horizontal"
    android:paddingHorizontal="16dp"
    android:paddingVertical="12dp">

    <!-- 歌曲索引 -->
    <TextView
        android:id="@+id/tv_song_index"
        android:layout_width="32dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textColor="#CCCCCC"
        android:textSize="14sp"
        tools:text="1" />

    <!-- 播放指示器 -->
    <ImageView
        android:id="@+id/iv_playing_indicator"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginStart="4dp"
        android:src="@drawable/ic_volume_up"
        android:tint="#1976D2"
        android:visibility="gone"
        tools:visibility="visible" />

    <!-- 歌曲信息 -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <!-- 歌曲标题 -->
        <TextView
            android:id="@+id/tv_song_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:textStyle="normal"
            tools:text="歌曲名称" />

        <!-- 艺术家 -->
        <TextView
            android:id="@+id/tv_song_artist"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="#CCCCCC"
            android:textSize="14sp"
            tools:text="艺术家名称" />

    </LinearLayout>

    <!-- 时长 -->
    <TextView
        android:id="@+id/tv_song_duration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:minWidth="48dp"
        android:textColor="#CCCCCC"
        android:textSize="14sp"
        android:gravity="center"
        tools:text="03:45" />

    <!-- 更多按钮 -->
    <ImageView
        android:id="@+id/iv_more"
        android:layout_width="@dimen/touch_target_size"
        android:layout_height="@dimen/touch_target_size"
        android:layout_marginStart="4dp"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        android:padding="12dp"
        android:src="@drawable/ic_more_vert"
        android:tint="#CCCCCC" />

</LinearLayout>
