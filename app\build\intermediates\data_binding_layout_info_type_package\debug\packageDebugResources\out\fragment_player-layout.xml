<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_player" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\fragment_player.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout"><Targets><Target tag="layout/fragment_player_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="409" endOffset="16"/></Target><Target id="@+id/background_blur" view="ImageView"><Expressions/><location startLine="8" startOffset="4" endLine="14" endOffset="29"/></Target><Target id="@+id/background_overlay" view="View"><Expressions/><location startLine="17" startOffset="4" endLine="22" endOffset="29"/></Target><Target id="@+id/content_container" view="LinearLayout"><Expressions/><location startLine="25" startOffset="4" endLine="226" endOffset="18"/></Target><Target id="@+id/album_cover_view" view="com.example.aimusicplayer.ui.widget.AlbumCoverView"><Expressions/><location startLine="41" startOffset="12" endLine="46" endOffset="49"/></Target><Target id="@+id/album_art" view="ImageView"><Expressions/><location startLine="49" startOffset="12" endLine="60" endOffset="57"/></Target><Target id="@+id/vinyl_background" view="ImageView"><Expressions/><location startLine="63" startOffset="12" endLine="71" endOffset="43"/></Target><Target id="@+id/song_title" view="TextView"><Expressions/><location startLine="83" startOffset="16" endLine="102" endOffset="46"/></Target><Target id="@+id/song_artist" view="TextView"><Expressions/><location startLine="104" startOffset="16" endLine="123" endOffset="46"/></Target><Target id="@+id/search_container" view="RelativeLayout"><Expressions/><location startLine="136" startOffset="12" endLine="177" endOffset="28"/></Target><Target id="@+id/search_edit_text" view="EditText"><Expressions/><location startLine="145" startOffset="16" endLine="163" endOffset="50"/></Target><Target id="@+id/search_button" view="ImageView"><Expressions/><location startLine="166" startOffset="16" endLine="176" endOffset="46"/></Target><Target id="@+id/search_suggestions_recycler" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="180" startOffset="12" endLine="190" endOffset="56"/></Target><Target id="@+id/search_results_recycler" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="193" startOffset="12" endLine="202" endOffset="41"/></Target><Target id="@+id/tab_layout_player" view="com.google.android.material.tabs.TabLayout"><Expressions/><location startLine="205" startOffset="12" endLine="215" endOffset="43"/></Target><Target id="@+id/view_pager_player" view="androidx.viewpager2.widget.ViewPager2"><Expressions/><location startLine="218" startOffset="12" endLine="224" endOffset="54"/></Target><Target id="@+id/loading_view" view="com.example.aimusicplayer.ui.widget.LottieLoadingView"><Expressions/><location startLine="229" startOffset="4" endLine="239" endOffset="25"/></Target><Target id="@+id/control_container" view="LinearLayout"><Expressions/><location startLine="242" startOffset="4" endLine="408" endOffset="18"/></Target><Target id="@+id/textview_player_current_time" view="TextView"><Expressions/><location startLine="262" startOffset="12" endLine="270" endOffset="38"/></Target><Target id="@+id/seekbar_player_progress" view="SeekBar"><Expressions/><location startLine="272" startOffset="12" endLine="282" endOffset="43"/></Target><Target id="@+id/textview_player_total_time" view="TextView"><Expressions/><location startLine="284" startOffset="12" endLine="292" endOffset="38"/></Target><Target id="@+id/button_player_playlist" view="ImageView"><Expressions/><location startLine="304" startOffset="12" endLine="316" endOffset="41"/></Target><Target id="@+id/button_player_play_mode" view="ImageView"><Expressions/><location startLine="319" startOffset="12" endLine="331" endOffset="41"/></Target><Target id="@+id/button_player_prev" view="ImageView"><Expressions/><location startLine="334" startOffset="12" endLine="346" endOffset="41"/></Target><Target id="@+id/button_player_play_pause" view="ImageView"><Expressions/><location startLine="349" startOffset="12" endLine="361" endOffset="51"/></Target><Target id="@+id/button_player_next" view="ImageView"><Expressions/><location startLine="364" startOffset="12" endLine="376" endOffset="41"/></Target><Target id="@+id/button_player_comment" view="ImageView"><Expressions/><location startLine="379" startOffset="12" endLine="391" endOffset="41"/></Target><Target id="@+id/button_player_collect" view="ImageView"><Expressions/><location startLine="394" startOffset="12" endLine="406" endOffset="41"/></Target></Targets></Layout>