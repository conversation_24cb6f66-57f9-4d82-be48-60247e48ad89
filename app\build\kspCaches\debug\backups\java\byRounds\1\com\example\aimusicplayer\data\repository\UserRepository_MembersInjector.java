package com.example.aimusicplayer.data.repository;

import com.example.aimusicplayer.data.cache.ApiCacheManager;
import com.example.aimusicplayer.network.ApiCallStrategy;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UserRepository_MembersInjector implements MembersInjector<UserRepository> {
  private final Provider<ApiCacheManager> apiCacheManagerProvider;

  private final Provider<ApiCallStrategy> apiCallStrategyProvider;

  public UserRepository_MembersInjector(Provider<ApiCacheManager> apiCacheManagerProvider,
      Provider<ApiCallStrategy> apiCallStrategyProvider) {
    this.apiCacheManagerProvider = apiCacheManagerProvider;
    this.apiCallStrategyProvider = apiCallStrategyProvider;
  }

  public static MembersInjector<UserRepository> create(
      Provider<ApiCacheManager> apiCacheManagerProvider,
      Provider<ApiCallStrategy> apiCallStrategyProvider) {
    return new UserRepository_MembersInjector(apiCacheManagerProvider, apiCallStrategyProvider);
  }

  @Override
  public void injectMembers(UserRepository instance) {
    BaseRepository_MembersInjector.injectApiCacheManager(instance, apiCacheManagerProvider.get());
    BaseRepository_MembersInjector.injectApiCallStrategy(instance, apiCallStrategyProvider.get());
  }
}
