package com.example.aimusicplayer.network;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ApiCallStrategy_Factory implements Factory<ApiCallStrategy> {
  @Override
  public ApiCallStrategy get() {
    return newInstance();
  }

  public static ApiCallStrategy_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static ApiCallStrategy newInstance() {
    return new ApiCallStrategy();
  }

  private static final class InstanceHolder {
    private static final ApiCallStrategy_Factory INSTANCE = new ApiCallStrategy_Factory();
  }
}
