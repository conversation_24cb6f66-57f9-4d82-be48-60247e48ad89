package com.example.aimusicplayer.network

import android.util.Log
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong
import javax.inject.Inject
import javax.inject.Singleton

/**
 * API调用策略管理器
 * 实现智能重试机制、频率控制、备用接口策略
 */
@Singleton
class ApiCallStrategy @Inject constructor() {

    companion object {
        private const val TAG = "ApiCallStrategy"

        // 调用频率限制
        const val LOGIN_STATUS_CHECK_INTERVAL = 30_000L // 30秒
        const val SEARCH_DEBOUNCE_DELAY = 300L // 300ms
        const val USER_INFO_CACHE_DURATION = 120_000L // 2分钟

        // 重试配置
        const val MAX_RETRY_COUNT = 3
        const val RETRY_BASE_DELAY = 1000L // 1秒

        // 接口类型
        const val API_TYPE_LOGIN_STATUS = "login_status"
        const val API_TYPE_SEARCH = "search"
        const val API_TYPE_USER_INFO = "user_info"
        const val API_TYPE_SONG_DETAIL = "song_detail"
        const val API_TYPE_LYRIC = "lyric"
    }

    // 最后调用时间记录
    private val lastCallTimes = ConcurrentHashMap<String, AtomicLong>()

    // 缓存数据
    private val cachedData = ConcurrentHashMap<String, Pair<Any, Long>>()

    // 搜索防抖动
    private var searchJob: Job? = null

    /**
     * 检查是否可以调用API（频率控制）
     */
    fun canCallApi(apiType: String): Boolean {
        val currentTime = System.currentTimeMillis()
        val lastCallTime = lastCallTimes.getOrPut(apiType) { AtomicLong(0) }

        return when (apiType) {
            API_TYPE_LOGIN_STATUS -> {
                val interval = currentTime - lastCallTime.get()
                interval >= LOGIN_STATUS_CHECK_INTERVAL
            }
            API_TYPE_USER_INFO -> {
                val interval = currentTime - lastCallTime.get()
                interval >= USER_INFO_CACHE_DURATION
            }
            else -> true // 其他接口不限制频率
        }
    }

    /**
     * 记录API调用时间
     */
    fun recordApiCall(apiType: String) {
        lastCallTimes.getOrPut(apiType) { AtomicLong(0) }
            .set(System.currentTimeMillis())
    }

    /**
     * 搜索防抖动
     */
    fun debouncedSearch(
        keywords: String,
        searchAction: suspend (String) -> Unit
    ) {
        searchJob?.cancel()
        searchJob = CoroutineScope(Dispatchers.Main).launch {
            delay(SEARCH_DEBOUNCE_DELAY)
            searchAction(keywords)
        }
    }

    /**
     * 智能API调用（带重试和降级策略）
     */
    suspend fun <T> smartApiCall(
        apiType: String,
        cacheKey: String? = null,
        primaryCall: suspend () -> T,
        fallbackCall: (suspend () -> T)? = null,
        defaultValue: T? = null
    ): ApiResult<T> {
        // 1. 检查频率限制
        if (!canCallApi(apiType)) {
            Log.d(TAG, "API调用频率限制: $apiType")
            // 尝试从缓存获取
            cacheKey?.let { key ->
                getCachedData<T>(key)?.let { cached ->
                    return ApiResult.Success(cached)
                }
            }
            return ApiResult.Error("调用过于频繁，请稍后再试")
        }

        // 2. 尝试从缓存获取（如果有缓存键）
        cacheKey?.let { key ->
            getCachedData<T>(key)?.let { cached ->
                Log.d(TAG, "使用缓存数据: $key")
                return ApiResult.Success(cached)
            }
        }

        // 3. 尝试主接口
        var lastException: Exception? = null
        repeat(MAX_RETRY_COUNT) { retryIndex ->
            try {
                val result = primaryCall()
                recordApiCall(apiType)

                // 缓存结果
                cacheKey?.let { key ->
                    cacheData(key, result)
                }

                Log.d(TAG, "主接口调用成功: $apiType")
                return ApiResult.Success(result)
            } catch (e: Exception) {
                lastException = e
                Log.w(TAG, "主接口调用失败 (重试 ${retryIndex + 1}/$MAX_RETRY_COUNT): $apiType", e)

                if (retryIndex < MAX_RETRY_COUNT - 1) {
                    delay(RETRY_BASE_DELAY * (retryIndex + 1)) // 指数退避
                }
            }
        }

        // 4. 尝试备用接口
        fallbackCall?.let { fallback ->
            repeat(MAX_RETRY_COUNT) { retryIndex ->
                try {
                    val result = fallback()
                    recordApiCall(apiType)

                    // 缓存结果
                    cacheKey?.let { key ->
                        cacheData(key, result)
                    }

                    Log.d(TAG, "备用接口调用成功: $apiType")
                    return ApiResult.Success(result)
                } catch (e: Exception) {
                    Log.w(TAG, "备用接口调用失败 (重试 ${retryIndex + 1}/$MAX_RETRY_COUNT): $apiType", e)

                    if (retryIndex < MAX_RETRY_COUNT - 1) {
                        delay(RETRY_BASE_DELAY * (retryIndex + 1))
                    }
                }
            }
        }

        // 5. 尝试从过期缓存获取
        cacheKey?.let { key ->
            getCachedData<T>(key, ignoreExpiration = true)?.let { cached ->
                Log.d(TAG, "使用过期缓存数据: $key")
                return ApiResult.Success(cached)
            }
        }

        // 6. 使用默认值
        defaultValue?.let { default ->
            Log.d(TAG, "使用默认值: $apiType")
            return ApiResult.Success(default)
        }

        // 7. 返回错误
        val errorMessage = lastException?.message ?: "网络请求失败"
        Log.e(TAG, "所有策略都失败: $apiType - $errorMessage")
        return ApiResult.Error(errorMessage)
    }

    /**
     * 获取缓存数据
     */
    @Suppress("UNCHECKED_CAST")
    private fun <T> getCachedData(key: String, ignoreExpiration: Boolean = false): T? {
        val cached = cachedData[key] ?: return null
        val (data, timestamp) = cached

        if (!ignoreExpiration) {
            val currentTime = System.currentTimeMillis()
            val cacheAge = currentTime - timestamp
            val maxAge = getCacheDuration(key)

            if (cacheAge > maxAge) {
                cachedData.remove(key)
                return null
            }
        }

        return data as? T
    }

    /**
     * 缓存数据
     */
    private fun <T> cacheData(key: String, data: T) {
        val timestamp = System.currentTimeMillis()
        cachedData[key] = Pair(data as Any, timestamp)

        // 清理过期缓存
        cleanExpiredCache()
    }

    /**
     * 获取缓存持续时间
     */
    private fun getCacheDuration(apiType: String): Long {
        return when (apiType) {
            API_TYPE_USER_INFO -> USER_INFO_CACHE_DURATION
            API_TYPE_LOGIN_STATUS -> LOGIN_STATUS_CHECK_INTERVAL
            API_TYPE_SONG_DETAIL -> 300_000L // 5分钟
            API_TYPE_LYRIC -> 600_000L // 10分钟
            else -> 120_000L // 默认2分钟
        }
    }

    /**
     * 清理过期缓存
     */
    private fun cleanExpiredCache() {
        val currentTime = System.currentTimeMillis()
        val iterator = cachedData.iterator()

        while (iterator.hasNext()) {
            val entry = iterator.next()
            val (_, timestamp) = entry.value
            val cacheAge = currentTime - timestamp
            val maxAge = getCacheDuration(entry.key)

            if (cacheAge > maxAge) {
                iterator.remove()
            }
        }
    }

    /**
     * API调用结果
     */
    sealed class ApiResult<out T> {
        data class Success<T>(val data: T) : ApiResult<T>()
        data class Error(val message: String) : ApiResult<Nothing>()

        fun isSuccess(): Boolean = this is Success
        fun isError(): Boolean = this is Error

        fun getOrNull(): T? = if (this is Success) data else null
        fun getErrorMessage(): String? = if (this is Error) message else null
    }
}
