<?xml version="1.0" encoding="utf-8"?>
<shape xmlns:android="http://schemas.android.com/apk/res/android"
    android:shape="oval">

    <!-- 蓝色渐变背景，正圆形，不贴着容器边框 -->
    <gradient
        android:startColor="#FF4A90E2"
        android:centerColor="#FF3A80D8"
        android:endColor="#FF2E7BD6"
        android:type="radial"
        android:gradientRadius="40dp" />

    <!-- 白色边框，增加阴影效果 -->
    <stroke
        android:width="3dp"
        android:color="#FFFFFF" />

    <!-- 设置固定尺寸确保正圆形，留出空白不贴边框 -->
    <size
        android:width="80dp"
        android:height="80dp" />

</shape>
