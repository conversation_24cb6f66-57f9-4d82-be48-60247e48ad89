package com.example.aimusicplayer.network

import android.util.Log
import okhttp3.Interceptor
import okhttp3.Response
import java.io.IOException
import java.net.SocketTimeoutException

/**
 * 超时处理拦截器
 * 处理网络超时和连接问题
 */
class TimeoutInterceptor : Interceptor {
    
    companion object {
        private const val TAG = "TimeoutInterceptor"
    }
    
    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        
        return try {
            val response = chain.proceed(request)
            
            // 检查响应状态
            if (!response.isSuccessful) {
                Log.w(TAG, "请求失败: ${request.url} - 状态码: ${response.code}")
            }
            
            response
        } catch (e: SocketTimeoutException) {
            Log.e(TAG, "请求超时: ${request.url}", e)
            throw IOException("网络请求超时，请检查网络连接", e)
        } catch (e: IOException) {
            Log.e(TAG, "网络错误: ${request.url}", e)
            throw IOException("网络连接失败，请检查网络设置", e)
        } catch (e: Exception) {
            Log.e(TAG, "未知错误: ${request.url}", e)
            throw IOException("请求失败，请稍后重试", e)
        }
    }
}
