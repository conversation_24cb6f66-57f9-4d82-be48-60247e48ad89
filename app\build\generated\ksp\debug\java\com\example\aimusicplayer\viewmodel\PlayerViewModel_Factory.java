package com.example.aimusicplayer.viewmodel;

import android.app.Application;
import com.example.aimusicplayer.data.repository.MusicRepository;
import com.example.aimusicplayer.error.GlobalErrorHandler;
import com.example.aimusicplayer.network.ApiCallStrategy;
import com.example.aimusicplayer.service.PlayerController;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PlayerViewModel_Factory implements Factory<PlayerViewModel> {
  private final Provider<Application> applicationProvider;

  private final Provider<PlayerController> playerControllerProvider;

  private final Provider<MusicRepository> musicRepositoryProvider;

  private final Provider<ApiCallStrategy> apiCallStrategyProvider;

  private final Provider<GlobalErrorHandler> errorHandlerProvider;

  public PlayerViewModel_Factory(Provider<Application> applicationProvider,
      Provider<PlayerController> playerControllerProvider,
      Provider<MusicRepository> musicRepositoryProvider,
      Provider<ApiCallStrategy> apiCallStrategyProvider,
      Provider<GlobalErrorHandler> errorHandlerProvider) {
    this.applicationProvider = applicationProvider;
    this.playerControllerProvider = playerControllerProvider;
    this.musicRepositoryProvider = musicRepositoryProvider;
    this.apiCallStrategyProvider = apiCallStrategyProvider;
    this.errorHandlerProvider = errorHandlerProvider;
  }

  @Override
  public PlayerViewModel get() {
    return newInstance(applicationProvider.get(), playerControllerProvider.get(), musicRepositoryProvider.get(), apiCallStrategyProvider.get(), errorHandlerProvider.get());
  }

  public static PlayerViewModel_Factory create(Provider<Application> applicationProvider,
      Provider<PlayerController> playerControllerProvider,
      Provider<MusicRepository> musicRepositoryProvider,
      Provider<ApiCallStrategy> apiCallStrategyProvider,
      Provider<GlobalErrorHandler> errorHandlerProvider) {
    return new PlayerViewModel_Factory(applicationProvider, playerControllerProvider, musicRepositoryProvider, apiCallStrategyProvider, errorHandlerProvider);
  }

  public static PlayerViewModel newInstance(Application application,
      PlayerController playerController, MusicRepository musicRepository,
      ApiCallStrategy apiCallStrategy, GlobalErrorHandler errorHandler) {
    return new PlayerViewModel(application, playerController, musicRepository, apiCallStrategy, errorHandler);
  }
}
